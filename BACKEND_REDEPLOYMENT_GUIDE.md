# PI Lawyer AI - Complete Backend Redeployment Guide

## 🎯 **Overview**

This is the **authoritative guide** for redeploying all PI Lawyer AI backend services. It covers both Fly.io services and Google Cloud microservices with comprehensive validation, rollback procedures, and troubleshooting.

## 🏗️ **Infrastructure Architecture**

### **Primary Services (Fly.io)**
| Environment | App Name | URL | Configuration | Status |
|-------------|----------|-----|---------------|---------|
| **Staging** | `pi-lawyer-ai-backend-stg` | `https://pi-lawyer-ai-backend-stg.fly.dev` | `fly.staging.toml` | Active |
| **Production** | `pi-lawyer-langgraph` | `https://pi-lawyer-langgraph.fly.dev` | `fly.production.toml` | Suspended |

### **Microservices (Google Cloud Run)**
| Service | Purpose | Staging Project | Production Project |
|---------|---------|-----------------|-------------------|
| **LangExtract** | Medical document extraction (HIPAA) | `newtexaslaw-1738585844702` | TBD |
| **Insurance-Parse** | Insurance document parsing | `newtexaslaw-1738585844702` | TBD |
| **Demand-Drafter** | Legal demand letter generation | `newtexaslaw-1738585844702` | TBD |
| **Comms-Ingest** | Communication processing | `newtexaslaw-1738585844702` | TBD |

### **Supporting Services**
- **Cloud Functions**: Tenant onboarding automation
- **Celery Workers**: Background job processing
- **Task Embedding Service**: Task similarity and search

## 🔍 **Phase 1: Pre-Deployment Validation**

### **1.1 Environment Health Check**

```bash
# Check current Fly.io app status
fly status --app pi-lawyer-ai-backend-stg
fly status --app pi-lawyer-langgraph

# Test current endpoints
curl -s https://pi-lawyer-ai-backend-stg.fly.dev/health | jq '.'
curl -s https://pi-lawyer-langgraph.fly.dev/health | jq '.'

# Check Google Cloud services (if authenticated)
gcloud run services list --project=newtexaslaw-1738585844702 --format="table(SERVICE_NAME,REGION,URL,LAST_MODIFIER)"
```

### **1.2 Database Connectivity Validation**

```bash
# Test Supabase connectivity
curl -H "apikey: $NEXT_PUBLIC_SUPABASE_ANON_KEY" \
     -H "Authorization: Bearer $NEXT_PUBLIC_SUPABASE_ANON_KEY" \
     "https://btwaueeckvylrlrnbvgt.supabase.co/rest/v1/" | jq '.'

# Production database test
curl -H "apikey: $NEXT_PUBLIC_SUPABASE_ANON_KEY" \
     -H "Authorization: Bearer $NEXT_PUBLIC_SUPABASE_ANON_KEY" \
     "https://anwefmklplkjxkmzpnva.supabase.co/rest/v1/" | jq '.'
```

### **1.3 Environment Configuration Validation**

```bash
# Verify critical environment variables exist
echo "Checking staging environment..."
grep -E "(SUPABASE_URL|OPENAI_API_KEY|LANGGRAPH_API_URL)" .env.staging

echo "Checking production environment..."
grep -E "(SUPABASE_URL|OPENAI_API_KEY|LANGGRAPH_API_URL)" .env.production

# Validate Fly.io secrets
fly secrets list --app pi-lawyer-ai-backend-stg
fly secrets list --app pi-lawyer-langgraph
```

### **1.4 Docker Build Validation**

```bash
# Test Docker build locally
docker build -f Dockerfile.backend -t pi-lawyer-test .

# Verify critical dependencies
docker run --rm pi-lawyer-test python -c "
import fastapi
import uvicorn
import copilotkit
print('✅ All critical dependencies available')
"
```

## 🚀 **Phase 2: Staging Deployment**

### **2.1 Deploy LangGraph API to Staging**

```bash
# Resume staging app if needed
fly resume --app pi-lawyer-ai-backend-stg

# Deploy with staging configuration
fly deploy --config fly.staging.toml --app pi-lawyer-ai-backend-stg

# Verify deployment
fly status --app pi-lawyer-ai-backend-stg
curl https://pi-lawyer-ai-backend-stg.fly.dev/health

# Check logs for any issues
fly logs --app pi-lawyer-ai-backend-stg --lines 50
```

### **2.2 Deploy Staging Microservices**

```bash
# Set staging project context
gcloud config set project newtexaslaw-1738585844702

# Deploy shared infrastructure first
gcloud deployment-manager deployments create microservices-shared-staging \
  --config shared-microservices-infrastructure.yaml

# Deploy microservices in dependency order
cd langextract-microservice
gcloud builds submit --config cloudbuild.yaml
cd ..

cd packages/insurance-battle-station/insurance-parse
gcloud builds submit --config cloudbuild.yaml
cd ../../..

cd packages/insurance-battle-station/demand-drafter
gcloud builds submit --config cloudbuild.yaml
cd ../../..

cd packages/insurance-battle-station/comms-ingest
gcloud builds submit --config cloudbuild.yaml
cd ../../..
```

### **2.3 Staging Integration Testing**

```bash
# Run comprehensive integration tests
./run-integration-tests.sh

# Test critical endpoints
curl -X POST https://langextract-staging-548331001248.us-central1.run.app/extract \
  -H "Content-Type: application/json" \
  -d '{"text": "Patient has diabetes", "extract_types": ["medications"]}'

# Test LangGraph API integration
curl -X POST https://pi-lawyer-ai-backend-stg.fly.dev/api/copilotkit/langgraph \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "test"}]}'
```

## 🎯 **Phase 3: Production Deployment**

### **3.1 Resume and Prepare Production App**

```bash
# Resume suspended production app
fly resume --app pi-lawyer-langgraph

# Verify app is ready
fly status --app pi-lawyer-langgraph

# Check machine configuration
fly machine list --app pi-lawyer-langgraph
```

### **3.2 Deploy LangGraph API to Production**

```bash
# Deploy to production
fly deploy --config fly.production.toml --app pi-lawyer-langgraph

# Verify deployment
fly status --app pi-lawyer-langgraph
curl https://pi-lawyer-langgraph.fly.dev/health

# Monitor deployment logs
fly logs --app pi-lawyer-langgraph --lines 100
```

### **3.3 Setup Production Google Cloud Project**

```bash
# Create production project (if not exists)
gcloud projects create pi-lawyer-ai-production --name="Pi Lawyer AI Production"

# Set billing account (replace with actual billing account ID)
gcloud billing projects link pi-lawyer-ai-production --billing-account=BILLING_ACCOUNT_ID

# Enable required services
gcloud services enable cloudbuild.googleapis.com run.googleapis.com \
  artifactregistry.googleapis.com secretmanager.googleapis.com \
  --project=pi-lawyer-ai-production

# Set up IAM permissions
gcloud projects add-iam-policy-binding pi-lawyer-ai-production \
  --member="user:<EMAIL>" \
  --role="roles/run.admin"
```

### **3.4 Deploy Production Microservices**

```bash
# Switch to production project
gcloud config set project pi-lawyer-ai-production

# Create production secrets from .env.production
gcloud secrets create SUPABASE_URL --data-file=<(grep SUPABASE_URL .env.production | cut -d'=' -f2)
gcloud secrets create OPENAI_API_KEY --data-file=<(grep OPENAI_API_KEY .env.production | cut -d'=' -f2)
# ... (repeat for all production secrets)

# Deploy shared infrastructure
gcloud deployment-manager deployments create microservices-shared-production \
  --config shared-microservices-infrastructure.yaml

# Deploy microservices with production configuration
# (Update cloudbuild.yaml files to use production substitutions)
cd langextract-microservice
gcloud builds submit --config cloudbuild.yaml --substitutions=_DEPLOY_ENV=production
cd ..

# Repeat for other microservices...
```

## 🔍 **Phase 4: Post-Deployment Verification**

### **4.1 Health Check Validation**

```bash
# Comprehensive health checks
echo "=== Staging Health Checks ==="
curl -s https://pi-lawyer-ai-backend-stg.fly.dev/health | jq '.'

echo "=== Production Health Checks ==="
curl -s https://pi-lawyer-langgraph.fly.dev/health | jq '.'

echo "=== Microservices Health Checks ==="
# Check each microservice health endpoint
for service in langextract insurance-parse demand-drafter comms-ingest; do
  echo "Checking $service..."
  # URLs will be provided after deployment
done
```

### **4.2 End-to-End Testing**

```bash
# Test complete workflow
curl -X POST https://pi-lawyer-langgraph.fly.dev/agents/master/invoke \
  -H "Content-Type: application/json" \
  -d '{
    "state": {
      "messages": [{"type": "human", "content": "Test deployment"}],
      "tenant_id": "test-tenant",
      "user_id": "test-user"
    }
  }'
```

## 🚨 **Emergency Rollback Procedures**

### **Fly.io Rollback**

```bash
# List recent deployments
fly releases --app pi-lawyer-langgraph

# Rollback to previous version
fly releases rollback --app pi-lawyer-langgraph

# Verify rollback
fly status --app pi-lawyer-langgraph
curl https://pi-lawyer-langgraph.fly.dev/health
```

### **Microservices Rollback**

```bash
# Use provided rollback script
./rollback-staging.sh

# Or manual rollback
gcloud run services update SERVICE_NAME \
  --image=PREVIOUS_IMAGE_URL \
  --region=us-central1
```

## 📊 **Monitoring and Troubleshooting**

### **Common Issues and Solutions**

| Issue | Symptoms | Solution |
|-------|----------|----------|
| Health check timeout | 503 errors, deployment fails | Check `/health` endpoint, increase timeout |
| Environment variables missing | 500 errors, auth failures | Verify `fly secrets` and GCP Secret Manager |
| Database connectivity | Connection errors | Check Supabase URLs and credentials |
| Memory limits | OOMKilled errors | Increase memory allocation in config |

### **Debug Commands**

```bash
# Fly.io debugging
fly ssh console --app pi-lawyer-langgraph
fly logs --app pi-lawyer-langgraph --lines 200

# Google Cloud debugging
gcloud run services describe SERVICE_NAME --region=us-central1
gcloud logs read "resource.type=cloud_run_revision" --limit=100
```

## 📋 **Deployment Checklist**

### **Pre-Deployment**
- [ ] All environment variables validated
- [ ] Docker build succeeds locally
- [ ] Database connectivity confirmed
- [ ] Backup procedures in place

### **Staging Deployment**
- [ ] LangGraph API deployed successfully
- [ ] All 4 microservices deployed
- [ ] Integration tests pass
- [ ] Health checks green

### **Production Deployment**
- [ ] Staging validation complete
- [ ] Production app resumed
- [ ] LangGraph API deployed
- [ ] Production GCP project configured
- [ ] Microservices deployed with production config
- [ ] End-to-end tests pass

### **Post-Deployment**
- [ ] All health checks green
- [ ] Monitoring configured
- [ ] Documentation updated
- [ ] Team notified

## 🔧 **Environment-Specific Configurations**

### **Staging Environment Variables**
```bash
# Key staging configurations from .env.staging
NEXT_PUBLIC_SUPABASE_URL=https://btwaueeckvylrlrnbvgt.supabase.co
LANGGRAPH_API_URL=https://pi-lawyer-ai-backend-stg.fly.dev
ENVIRONMENT=staging
TARGET_ENVIRONMENT=staging
STRIPE_SECRET_KEY=sk_test_... # Test mode
```

### **Production Environment Variables**
```bash
# Key production configurations from .env.production
NEXT_PUBLIC_SUPABASE_URL=https://anwefmklplkjxkmzpnva.supabase.co
LANGGRAPH_API_URL=https://pi-lawyer-langgraph.fly.dev
ENVIRONMENT=production
TARGET_ENVIRONMENT=production
STRIPE_SECRET_KEY=sk_live_... # Live mode
```

### **Critical Fly.io Secrets**
```bash
# Set these secrets for each environment
fly secrets set --app APP_NAME \
  OPENAI_API_KEY="sk-proj-..." \
  SUPABASE_URL="https://..." \
  SUPABASE_SERVICE_KEY="eyJ..." \
  VOYAGE_API_KEY="pa-..." \
  GROQ_API_KEY="gsk_..." \
  REDIS_URL="rediss://..." \
  CPK_ENDPOINT_SECRET="..."
```

## 🔄 **Automated Deployment Scripts**

### **Complete Staging Deployment**
```bash
#!/bin/bash
# deploy-staging.sh - Complete staging deployment

set -e

echo "🚀 Starting complete staging deployment..."

# 1. Deploy Fly.io service
echo "Deploying LangGraph API to staging..."
fly deploy --config fly.staging.toml --app pi-lawyer-ai-backend-stg

# 2. Deploy microservices
echo "Deploying microservices to staging..."
gcloud config set project newtexaslaw-1738585844702
./deploy-microservices.sh

# 3. Run integration tests
echo "Running integration tests..."
./run-integration-tests.sh

echo "✅ Staging deployment complete!"
```

### **Complete Production Deployment**
```bash
#!/bin/bash
# deploy-production.sh - Complete production deployment

set -e

echo "🚀 Starting complete production deployment..."

# 1. Resume and deploy Fly.io service
echo "Resuming and deploying LangGraph API to production..."
fly resume --app pi-lawyer-langgraph
fly deploy --config fly.production.toml --app pi-lawyer-langgraph

# 2. Deploy microservices (after GCP project setup)
echo "Deploying microservices to production..."
gcloud config set project pi-lawyer-ai-production
# Production microservices deployment commands here

# 3. Run smoke tests
echo "Running production smoke tests..."
curl -f https://pi-lawyer-langgraph.fly.dev/health

echo "✅ Production deployment complete!"
```

## 🔍 **Advanced Troubleshooting**

### **Fly.io Specific Issues**

#### **Machine Management**
```bash
# List all machines
fly machine list --app pi-lawyer-langgraph

# Destroy problematic machine
fly machine destroy MACHINE_ID --force --app pi-lawyer-langgraph

# Create new machine
fly machine run --app pi-lawyer-langgraph
```

#### **Volume Issues**
```bash
# List volumes
fly volumes list --app pi-lawyer-langgraph

# Create new volume if needed
fly volumes create pi_lawyer_data_production --region iad --size 10 --app pi-lawyer-langgraph
```

### **Google Cloud Specific Issues**

#### **Service Account Permissions**
```bash
# Grant necessary permissions to Cloud Run default service account
PROJECT_NUMBER=$(gcloud projects describe $PROJECT_ID --format="value(projectNumber)")
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"
```

#### **Container Registry Issues**
```bash
# Configure Docker for Artifact Registry
gcloud auth configure-docker us-central1-docker.pkg.dev

# List images in registry
gcloud artifacts docker images list us-central1-docker.pkg.dev/$PROJECT_ID/REPO_NAME
```

## 📈 **Performance Optimization**

### **Fly.io Scaling Configuration**
```toml
# In fly.production.toml
[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  max_machines_running = 5

[[http_service.checks]]
  interval = "30s"
  timeout = "10s"
  grace_period = "30s"
  method = "GET"
  path = "/health"
```

### **Google Cloud Run Optimization**
```yaml
# In cloudbuild.yaml
- name: "gcr.io/cloud-builders/gcloud"
  args:
    - "run"
    - "deploy"
    - "${_SERVICE_NAME}-${_DEPLOY_ENV}"
    - "--image=us-central1-docker.pkg.dev/$PROJECT_ID/${_SERVICE_NAME}-repo/${_SERVICE_NAME}:${_DEPLOY_ENV}-latest"
    - "--memory=2Gi"
    - "--cpu=2"
    - "--concurrency=100"
    - "--max-instances=10"
    - "--min-instances=1"
```

## 🔐 **Security Considerations**

### **Secret Management Best Practices**
1. **Never commit secrets** to version control
2. **Use environment-specific secrets** (staging vs production)
3. **Rotate secrets regularly** (quarterly recommended)
4. **Use least-privilege access** for service accounts
5. **Monitor secret access** through audit logs

### **Network Security**
```bash
# Configure VPC connector for internal service communication
gcloud compute networks vpc-access connectors create microservices-connector \
  --region=us-central1 \
  --subnet=default \
  --subnet-project=$PROJECT_ID
```

## 📚 **Related Documentation**

### **Primary References**
- [Fly.io Documentation](https://fly.io/docs/)
- [Google Cloud Run Documentation](https://cloud.google.com/run/docs)
- [Supabase API Documentation](https://supabase.com/docs/reference/api)

### **Project-Specific Guides**
- `DEPLOYMENT.md` - Basic deployment commands
- `LANGGRAPH-DEPLOYMENT.md` - LangGraph-specific setup
- `DEPLOYMENT_GUIDE.md` - Microservices deployment
- `MICROSERVICES_DEPLOYMENT_COMPLETE.md` - Microservices overview

### **Troubleshooting Guides**
- `LANGEXTRACT_AUTHENTICATION_TROUBLESHOOTING.md` - LangExtract auth issues
- `LANGEXTRACT_TROUBLESHOOTING_REFERENCE.md` - Comprehensive LangExtract guide
- `rollback-staging.sh` - Emergency rollback procedures

---

**Last Updated**: 2025-09-15
**Maintained By**: Development Team
**Version**: 1.0
**Status**: Authoritative Guide
