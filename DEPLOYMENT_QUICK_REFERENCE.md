# PI Lawyer AI - Deployment Quick Reference

## 🚀 **Essential Commands**

### **Fly.io Commands**
```bash
# Status and health checks
fly status --app pi-lawyer-ai-backend-stg
fly status --app pi-lawyer-langgraph
curl https://pi-lawyer-ai-backend-stg.fly.dev/health
curl https://pi-lawyer-langgraph.fly.dev/health

# Deployment
fly deploy --config fly.staging.toml --app pi-lawyer-ai-backend-stg
fly deploy --config fly.production.toml --app pi-lawyer-langgraph

# Resume suspended app
fly resume --app pi-lawyer-langgraph

# Logs and debugging
fly logs --app pi-lawyer-langgraph --lines 100
fly ssh console --app pi-lawyer-langgraph

# Secrets management
fly secrets list --app pi-lawyer-langgraph
fly secrets set OPENAI_API_KEY="sk-..." --app pi-lawyer-langgraph

# Rollback
fly releases --app pi-lawyer-langgraph
fly releases rollback --app pi-lawyer-langgraph
```

### **Google Cloud Commands**
```bash
# Project switching
gcloud config set project newtexaslaw-1738585844702  # Staging
gcloud config set project pi-lawyer-ai-production    # Production

# Service deployment
cd langextract-microservice && gcloud builds submit --config cloudbuild.yaml
cd packages/insurance-battle-station/insurance-parse && gcloud builds submit --config cloudbuild.yaml
cd packages/insurance-battle-station/demand-drafter && gcloud builds submit --config cloudbuild.yaml
cd packages/insurance-battle-station/comms-ingest && gcloud builds submit --config cloudbuild.yaml

# Service status
gcloud run services list --format="table(SERVICE_NAME,REGION,URL)"
gcloud run services describe SERVICE_NAME --region=us-central1

# Logs
gcloud logs read "resource.type=cloud_run_revision" --limit=50
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=langextract-staging" --limit=20
```

## 🔍 **Health Check URLs**

### **Staging Environment**
- **LangGraph API**: https://pi-lawyer-ai-backend-stg.fly.dev/health
- **LangExtract**: https://langextract-staging-548331001248.us-central1.run.app/health
- **Insurance-Parse**: https://insurance-parse-staging-548331001248.us-central1.run.app/health
- **Demand-Drafter**: https://demand-drafter-staging-548331001248.us-central1.run.app/health
- **Comms-Ingest**: https://comms-ingest-staging-548331001248.us-central1.run.app/health

### **Production Environment**
- **LangGraph API**: https://pi-lawyer-langgraph.fly.dev/health
- **Microservices**: URLs TBD after production deployment

## 🚨 **Emergency Procedures**

### **Quick Rollback**
```bash
# Fly.io rollback
fly releases rollback --app pi-lawyer-langgraph

# Microservices rollback
./rollback-staging.sh

# Manual service rollback
gcloud run services update SERVICE_NAME \
  --image=PREVIOUS_IMAGE_URL \
  --region=us-central1
```

### **Service Recovery**
```bash
# Restart Fly.io machine
fly machine restart MACHINE_ID --app pi-lawyer-langgraph

# Force new Cloud Run revision
gcloud run services update SERVICE_NAME \
  --region=us-central1 \
  --update-env-vars=RESTART_TRIGGER=$(date +%s)
```

## 📋 **Environment Variables**

### **Critical Variables to Verify**
```bash
# Staging
NEXT_PUBLIC_SUPABASE_URL=https://btwaueeckvylrlrnbvgt.supabase.co
LANGGRAPH_API_URL=https://pi-lawyer-ai-backend-stg.fly.dev
ENVIRONMENT=staging

# Production  
NEXT_PUBLIC_SUPABASE_URL=https://anwefmklplkjxkmzpnva.supabase.co
LANGGRAPH_API_URL=https://pi-lawyer-langgraph.fly.dev
ENVIRONMENT=production
```

## 🔧 **Troubleshooting**

### **Common Issues**
| Issue | Quick Fix |
|-------|-----------|
| Health check timeout | Check `/health` endpoint manually |
| 503 Service Unavailable | Restart service or check logs |
| Environment variables missing | Verify `fly secrets` or GCP Secret Manager |
| Build failures | Check Docker build locally first |
| Database connection errors | Verify Supabase URLs and keys |

### **Debug Commands**
```bash
# Test local Docker build
docker build -f Dockerfile.backend -t test-build .

# Test database connectivity
curl -H "apikey: $SUPABASE_ANON_KEY" "https://btwaueeckvylrlrnbvgt.supabase.co/rest/v1/"

# Check service logs
fly logs --app pi-lawyer-langgraph --lines 50
gcloud logs read "resource.type=cloud_run_revision" --limit=20
```

## 📊 **Deployment Status Check**

### **Complete Health Check Script**
```bash
#!/bin/bash
# health-check-all.sh

echo "=== Fly.io Services ==="
curl -s https://pi-lawyer-ai-backend-stg.fly.dev/health | jq '.status'
curl -s https://pi-lawyer-langgraph.fly.dev/health | jq '.status'

echo "=== Google Cloud Services ==="
# Add microservice health checks here
for service in langextract insurance-parse demand-drafter comms-ingest; do
  echo "Checking $service..."
  # curl -s https://$service-staging-548331001248.us-central1.run.app/health | jq '.status'
done
```

## 🎯 **Quick Deployment Workflows**

### **Staging Deployment**
```bash
# 1. Deploy Fly.io service
fly deploy --config fly.staging.toml --app pi-lawyer-ai-backend-stg

# 2. Deploy microservices
gcloud config set project newtexaslaw-1738585844702
./deploy-microservices.sh

# 3. Verify
curl https://pi-lawyer-ai-backend-stg.fly.dev/health
```

### **Production Deployment**
```bash
# 1. Resume and deploy Fly.io service
fly resume --app pi-lawyer-langgraph
fly deploy --config fly.production.toml --app pi-lawyer-langgraph

# 2. Deploy microservices (after GCP setup)
gcloud config set project pi-lawyer-ai-production
# Deploy microservices with production config

# 3. Verify
curl https://pi-lawyer-langgraph.fly.dev/health
```

## 📞 **Support Contacts**

- **Primary Documentation**: `BACKEND_REDEPLOYMENT_GUIDE.md`
- **Microservices Guide**: `DEPLOYMENT_GUIDE.md`
- **Emergency Rollback**: `rollback-staging.sh`
- **Integration Tests**: `run-integration-tests.sh`

---

**Quick Reference Version**: 1.0  
**Last Updated**: 2025-09-15  
**For detailed procedures, see**: `BACKEND_REDEPLOYMENT_GUIDE.md`
